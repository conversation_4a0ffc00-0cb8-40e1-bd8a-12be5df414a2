﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using TeklaTool.Utils;

namespace TeklaTool
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private static Mutex _mutex;

        protected override void OnStartup(StartupEventArgs e)
        {
            // 添加全局异常处理
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            DispatcherUnhandledException += App_DispatcherUnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

            // 检查Tekla版本兼容性
            CheckTeklaVersionCompatibility();

            base.OnStartup(e);

            bool createdNew = false;
            try
            {
                _mutex = new Mutex(true, "TeklaListWPF", out createdNew);
                if (!createdNew)
                {
                    Process currentProcess = Process.GetCurrentProcess();
                    var runningProcesses = Process.GetProcessesByName(currentProcess.ProcessName)
                        .Where(p => p.Id != currentProcess.Id);

                    if (runningProcesses.Any())
                    {
                        var result = MessageBox.Show(
                            "检测到已有TeklaListWPF在运行。\n\n点击[是] - 继续使用当前版本\n点击[否] - 停止当前版本并使用已运行的版本",
                            "版本冲突提示",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Exclamation);

                        if (result == MessageBoxResult.Yes)
                        {
                            foreach (var process in runningProcesses)
                            {
                                try
                                {
                                    process.Kill();
                                }
                                catch
                                {
                                    // 忽略关闭进程时的错误
                                }
                            }
                        }
                        else
                        {
                            Shutdown();
                            return;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("启动程序时发生错误：" + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        /// <summary>
        /// 检查Tekla版本兼容性
        /// </summary>
        private void CheckTeklaVersionCompatibility()
        {
            try
            {
                Logger.LogInfo("开始检查Tekla版本兼容性...");

                // 获取版本兼容性报告
                var report = TeklaVersionManager.GetCompatibilityReport();
                Logger.LogInfo($"Tekla版本兼容性报告:\n{report}");

                // 检查是否有安装的版本
                var installedVersions = TeklaVersionManager.GetInstalledTeklaVersions();
                if (!installedVersions.Any())
                {
                    Logger.LogWarning("未检测到已安装的Tekla Structures版本");

                    var result = MessageBox.Show(
                        "未检测到已安装的Tekla Structures版本。\n\n" +
                        "程序可能无法正常连接到Tekla模型。\n\n" +
                        "是否继续启动程序？",
                        "Tekla版本检测",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.No)
                    {
                        Shutdown();
                        return;
                    }
                }
                else
                {
                    Logger.LogInfo($"检测到 {installedVersions.Count} 个Tekla版本: {string.Join(", ", installedVersions)}");

                    // 验证程序集绑定重定向配置
                    if (!TeklaVersionManager.ValidateBindingRedirects())
                    {
                        Logger.LogWarning("程序集绑定重定向配置可能存在问题");
                    }
                }

                // 记录当前加载的版本
                var loadedVersion = TeklaVersionManager.GetLoadedTeklaVersion();
                Logger.LogInfo($"当前加载的Tekla程序集版本: {loadedVersion}");
            }
            catch (Exception ex)
            {
                Logger.LogError($"检查Tekla版本兼容性时发生错误: {ex.Message}");
                // 不阻止程序启动，只记录错误
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            if (_mutex != null)
            {
                _mutex.ReleaseMutex();
                _mutex.Dispose();
            }
            base.OnExit(e);
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                string errorMessage = exception != null
                    ? $"未处理的异常: {exception.Message}\n\n{exception.StackTrace}"
                    : "发生未知异常";

                Logger.LogError(errorMessage);

                MessageBox.Show(errorMessage, "程序错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaListWPF_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 严重错误，程序即将关闭\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
            finally
            {
                // 如果是终止性异常，程序将退出
                if (e.IsTerminating)
                {
                    MessageBox.Show("程序遇到严重错误，即将关闭。", "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"UI线程未处理的异常: {e.Exception.Message}\n\n{e.Exception.StackTrace}";
                Logger.LogError(errorMessage);

                MessageBox.Show(errorMessage, "UI错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已处理，防止应用程序崩溃
                e.Handled = true;
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaListWPF_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: UI线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }

        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"任务未处理的异常: {e.Exception.Message}\n\n{e.Exception.InnerException?.StackTrace}";
                Logger.LogError(errorMessage);

                MessageBox.Show(errorMessage, "任务错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已观察，防止应用程序崩溃
                e.SetObserved();
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaListWPF_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 任务线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }
    }
}
