using System;
using System.Windows;
using System.Windows.Media;
using TeklaTool.Utils;

namespace TeklaTool.Views
{
    /// <summary>
    /// TeklaVersionInfoWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TeklaVersionInfoWindow : Window
    {
        public TeklaVersionInfoWindow()
        {
            InitializeComponent();
            LoadVersionInfo();
        }

        /// <summary>
        /// 加载版本信息
        /// </summary>
        private void LoadVersionInfo()
        {
            try
            {
                StatusTextBlock.Text = "正在检查...";
                StatusTextBlock.Foreground = Brushes.Blue;

                // 获取版本兼容性报告
                var report = TeklaVersionManager.GetCompatibilityReport();
                VersionInfoTextBlock.Text = report;

                // 获取推荐配置
                var recommendedConfig = TeklaVersionManager.GetRecommendedConfiguration();
                VersionInfoTextBlock.Text += Environment.NewLine + Environment.NewLine + 
                                           "=== 推荐配置 ===" + Environment.NewLine + 
                                           recommendedConfig;

                // 检查状态
                var installedVersions = TeklaVersionManager.GetInstalledTeklaVersions();
                if (installedVersions.Count > 0)
                {
                    StatusTextBlock.Text = $"检测到 {installedVersions.Count} 个版本";
                    StatusTextBlock.Foreground = Brushes.Green;
                }
                else
                {
                    StatusTextBlock.Text = "未检测到Tekla版本";
                    StatusTextBlock.Foreground = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                VersionInfoTextBlock.Text = $"加载版本信息时发生错误: {ex.Message}";
                StatusTextBlock.Text = "检查失败";
                StatusTextBlock.Foreground = Brushes.Red;
                Logger.LogError($"加载版本信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadVersionInfo();
        }

        /// <summary>
        /// 复制信息按钮点击事件
        /// </summary>
        private void CopyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(VersionInfoTextBlock.Text))
                {
                    Clipboard.SetText(VersionInfoTextBlock.Text);
                    MessageBox.Show("版本信息已复制到剪贴板", "复制成功", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制失败: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
