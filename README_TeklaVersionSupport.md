# Tekla Structures 多版本支持方案

## 概述

本项目已配置为支持多个版本的Tekla Structures（19.0 - 21.1），使用程序集绑定重定向技术实现版本兼容性。

## 支持的版本

- Tekla Structures 19.0
- Tekla Structures 19.1  
- Tekla Structures 20.0
- Tekla Structures 21.0
- Tekla Structures 21.1

## 实现方案

### 1. 程序集绑定重定向

通过 `App.config` 文件中的 `<bindingRedirect>` 配置，将所有支持版本的程序集重定向到基准版本（19.0）。

### 2. 项目引用配置

项目文件中使用 `SpecificVersion=False` 设置，允许加载不同版本的程序集。

### 3. 版本检测机制

应用程序启动时自动检测：
- 系统中安装的Tekla版本
- GAC中可用的程序集版本
- 当前加载的程序集版本

## 配置文件说明

### App.config 关键配置

```xml
<runtime>
  <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
    <dependentAssembly>
      <assemblyIdentity name="Tekla.Structures.Model" 
                        publicKeyToken="2f04dbe497b71114" 
                        culture="neutral" />
      <bindingRedirect oldVersion="19.0.0.0-21.1.0.0" 
                       newVersion="19.0.0.0" />
    </dependentAssembly>
    <!-- 其他Tekla程序集的类似配置 -->
  </assemblyBinding>
</runtime>
```

### 项目文件关键配置

```xml
<Reference Include="Tekla.Structures.Model, Version=19.0.0.0, Culture=neutral, PublicKeyToken=2f04dbe497b71114, processorArchitecture=MSIL">
  <SpecificVersion>False</SpecificVersion>
  <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Model.dll</HintPath>
</Reference>
```

## 工作原理

1. **编译时**: 程序使用19.0版本的程序集进行编译
2. **运行时**: .NET Framework根据绑定重定向配置，自动将对19.0版本的调用重定向到实际安装的版本
3. **版本检测**: `TeklaVersionManager` 类负责检测和验证版本兼容性

## 使用说明

### 部署要求

1. 目标机器必须安装至少一个支持的Tekla Structures版本
2. Tekla程序集必须在GAC中注册（通常在安装Tekla时自动完成）
3. 确保App.config文件与可执行文件在同一目录

### 运行时行为

- 程序启动时会自动检测可用的Tekla版本
- 如果检测到多个版本，会使用最新的兼容版本
- 版本信息会记录在日志文件中
- 如果未检测到任何版本，会显示警告但允许继续运行

## 故障排除

### 常见问题

1. **程序集加载失败**
   - 检查Tekla Structures是否正确安装
   - 验证GAC中是否存在Tekla程序集
   - 检查App.config文件是否正确配置

2. **版本不兼容**
   - 确认Tekla版本在支持范围内（19.0-21.1）
   - 检查程序集的公钥令牌是否正确

3. **连接Tekla失败**
   - 确保Tekla Structures正在运行
   - 检查是否有模型文件打开
   - 验证用户权限

### 调试信息

启用详细日志记录：
```xml
<appSettings>
  <add key="EnableAssemblyBindingLog" value="true" />
  <add key="LogLevel" value="Debug" />
</appSettings>
```

## 版本管理API

### TeklaVersionManager 类

提供以下功能：
- `GetLoadedTeklaVersion()`: 获取当前加载的版本
- `GetInstalledTeklaVersions()`: 获取已安装的版本列表
- `IsVersionSupported()`: 检查版本是否支持
- `GetCompatibilityReport()`: 生成兼容性报告

### 使用示例

```csharp
// 检查版本兼容性
var installedVersions = TeklaVersionManager.GetInstalledTeklaVersions();
var currentVersion = TeklaVersionManager.GetLoadedTeklaVersion();
var report = TeklaVersionManager.GetCompatibilityReport();

Logger.LogInfo($"当前版本: {currentVersion}");
Logger.LogInfo($"兼容性报告: {report}");
```

## 注意事项

1. **API兼容性**: 虽然支持多版本，但应避免使用特定版本才有的新API
2. **性能考虑**: 程序集重定向可能会有轻微的性能开销
3. **测试建议**: 在不同版本的Tekla环境中测试程序功能
4. **更新维护**: 新版本发布时需要更新配置文件中的版本范围

## 扩展支持新版本

要支持新的Tekla版本（如22.0），需要：

1. 更新App.config中的版本范围：
```xml
<bindingRedirect oldVersion="19.0.0.0-22.0.0.0" newVersion="19.0.0.0" />
```

2. 在TeklaVersionManager中添加新版本：
```csharp
{ "22.0", "22.0.0.0" }
```

3. 测试新版本的兼容性
