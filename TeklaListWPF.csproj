﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <RootNamespace>TeklaTool</RootNamespace>
    <AssemblyName>TeklaListWPF</AssemblyName>
    <LangVersion>8.0</LangVersion>
    <Version>1.2.0.0</Version>
    <AssemblyVersion>1.2.0.0</AssemblyVersion>
    <FileVersion>1.2.0.0</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />

    <!-- Tekla Structures 程序集引用 - 使用GAC引用以支持多版本 -->
    <Reference Include="Tekla.Structures, Version=19.0.0.0, Culture=neutral, PublicKeyToken=2f04dbe497b71114, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.dll</HintPath>
    </Reference>

    <Reference Include="Tekla.Structures.Drawing, Version=19.0.0.0, Culture=neutral, PublicKeyToken=2f04dbe497b71114, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Drawing.dll</HintPath>
    </Reference>

    <Reference Include="Tekla.Structures.Model, Version=19.0.0.0, Culture=neutral, PublicKeyToken=2f04dbe497b71114, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Model.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
  </ItemGroup>

</Project>
