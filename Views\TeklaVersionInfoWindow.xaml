<Window x:Class="TeklaTool.Views.TeklaVersionInfoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Tekla版本兼容性信息" 
        Height="500" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="Tekla Structures 版本兼容性信息" 
                   FontSize="16" 
                   FontWeight="Bold" 
                   Margin="0,0,0,10"
                   HorizontalAlignment="Center"/>
        
        <!-- 版本信息显示区域 -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      Margin="0,0,0,10">
            <TextBlock x:Name="VersionInfoTextBlock"
                       FontFamily="Consolas"
                       FontSize="12"
                       Background="#F5F5F5"
                       Padding="10"
                       TextWrapping="Wrap"/>
        </ScrollViewer>
        
        <!-- 状态栏 -->
        <Border Grid.Row="2" 
                Background="#E0E0E0" 
                Padding="5"
                Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="状态: " FontWeight="Bold"/>
                <TextBlock x:Name="StatusTextBlock" 
                           Text="正在检查..." 
                           Foreground="Blue"/>
            </StackPanel>
        </Border>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button x:Name="RefreshButton" 
                    Content="刷新" 
                    Width="80" 
                    Height="30" 
                    Margin="0,0,10,0"
                    Click="RefreshButton_Click"/>
            <Button x:Name="CopyButton" 
                    Content="复制信息" 
                    Width="80" 
                    Height="30" 
                    Margin="0,0,10,0"
                    Click="CopyButton_Click"/>
            <Button x:Name="CloseButton" 
                    Content="关闭" 
                    Width="80" 
                    Height="30" 
                    IsDefault="True"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
