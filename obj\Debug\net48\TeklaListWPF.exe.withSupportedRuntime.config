<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <!-- Tekla.Structures.Model 程序集绑定重定向 -->
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures.Model" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="19.0.0.0-21.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
      <!-- Tekla.Structures 程序集绑定重定向 -->
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="19.0.0.0-21.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
      <!-- Tekla.Structures.Drawing 程序集绑定重定向 -->
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures.Drawing" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="19.0.0.0-21.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
      <!-- Tekla.Structures.Catalogs 程序集绑定重定向 -->
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures.Catalogs" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="19.0.0.0-21.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
      <!-- Tekla.Structures.Datatype 程序集绑定重定向 -->
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures.Datatype" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="19.0.0.0-21.1.0.0" newVersion="19.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
    <!-- 启用程序集加载失败时的详细错误信息 -->
    <loadFromRemoteSources enabled="true" />
    <!-- 启用程序集绑定日志记录（调试时使用） -->
    <!-- 
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="bin;lib" />
    </assemblyBinding>
    -->
  </runtime>
  <!-- 应用程序设置 -->
  <appSettings>
    <!-- Tekla版本兼容性设置 -->
    <add key="TeklaVersionCompatibility" value="true" />
    <add key="SupportedTeklaVersions" value="19.0,19.1,20.0,21.0,21.1" />
    <!-- 调试设置 -->
    <add key="EnableAssemblyBindingLog" value="false" />
    <add key="LogLevel" value="Info" />
  </appSettings>
</configuration>