using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Configuration;
using Microsoft.Win32;

namespace TeklaTool.Utils
{
    /// <summary>
    /// Tekla Structures 版本管理器
    /// 负责检测和管理多版本Tekla Structures的兼容性
    /// </summary>
    public static class TeklaVersionManager
    {
        private static readonly Dictionary<string, string> _supportedVersions = new Dictionary<string, string>
        {
            { "19.0", "19.0.0.0" },
            { "19.1", "19.1.0.0" },
            { "20.0", "20.0.0.0" },
            { "21.0", "21.0.0.0" },
            { "21.1", "21.1.0.0" }
        };

        /// <summary>
        /// 获取当前加载的Tekla程序集版本
        /// </summary>
        /// <returns>版本信息</returns>
        public static string GetLoadedTeklaVersion()
        {
            try
            {
                var assembly = Assembly.GetAssembly(typeof(Tekla.Structures.Model.Model));
                if (assembly != null)
                {
                    var version = assembly.GetName().Version;
                    return $"{version.Major}.{version.Minor}.{version.Build}.{version.Revision}";
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"获取Tekla程序集版本失败: {ex.Message}");
            }
            return "未知版本";
        }

        /// <summary>
        /// 检测系统中安装的Tekla Structures版本
        /// </summary>
        /// <returns>安装的版本列表</returns>
        public static List<string> GetInstalledTeklaVersions()
        {
            var installedVersions = new List<string>();

            try
            {
                // 检查注册表中的Tekla安装信息
                using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Tekla\Tekla Structures"))
                {
                    if (key != null)
                    {
                        foreach (var subKeyName in key.GetSubKeyNames())
                        {
                            if (_supportedVersions.ContainsKey(subKeyName))
                            {
                                installedVersions.Add(subKeyName);
                            }
                        }
                    }
                }

                // 检查GAC中的程序集
                var gacVersions = GetGacTeklaVersions();
                foreach (var version in gacVersions)
                {
                    if (!installedVersions.Contains(version))
                    {
                        installedVersions.Add(version);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"检测Tekla版本失败: {ex.Message}");
            }

            return installedVersions.OrderBy(v => v).ToList();
        }

        /// <summary>
        /// 从GAC中获取Tekla程序集版本
        /// </summary>
        /// <returns>GAC中的版本列表</returns>
        private static List<string> GetGacTeklaVersions()
        {
            var versions = new List<string>();
            
            try
            {
                var gacPath = @"C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Tekla.Structures.Model";
                if (Directory.Exists(gacPath))
                {
                    var versionDirs = Directory.GetDirectories(gacPath);
                    foreach (var dir in versionDirs)
                    {
                        var dirName = Path.GetFileName(dir);
                        // 解析版本号，格式如: v4.0_19.0.0.0__2f04dbe497b71114
                        if (dirName.StartsWith("v4.0_") && dirName.Contains("__2f04dbe497b71114"))
                        {
                            var versionPart = dirName.Substring(5, dirName.IndexOf("__") - 5);
                            var majorMinor = versionPart.Substring(0, versionPart.LastIndexOf('.', versionPart.LastIndexOf('.') - 1));
                            
                            if (_supportedVersions.ContainsValue(versionPart))
                            {
                                var key = _supportedVersions.FirstOrDefault(kv => kv.Value == versionPart).Key;
                                if (!string.IsNullOrEmpty(key) && !versions.Contains(key))
                                {
                                    versions.Add(key);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"从GAC获取Tekla版本失败: {ex.Message}");
            }

            return versions;
        }

        /// <summary>
        /// 验证版本兼容性
        /// </summary>
        /// <param name="targetVersion">目标版本</param>
        /// <returns>是否兼容</returns>
        public static bool IsVersionSupported(string targetVersion)
        {
            return _supportedVersions.ContainsKey(targetVersion);
        }

        /// <summary>
        /// 获取版本兼容性信息
        /// </summary>
        /// <returns>兼容性报告</returns>
        public static string GetCompatibilityReport()
        {
            var report = new List<string>();
            
            report.Add("=== Tekla Structures 版本兼容性报告 ===");
            report.Add($"当前加载版本: {GetLoadedTeklaVersion()}");
            report.Add("");
            
            var installedVersions = GetInstalledTeklaVersions();
            report.Add($"检测到的安装版本 ({installedVersions.Count}):");
            foreach (var version in installedVersions)
            {
                var supported = IsVersionSupported(version) ? "✓ 支持" : "✗ 不支持";
                report.Add($"  - {version} ({supported})");
            }
            
            report.Add("");
            report.Add("支持的版本范围:");
            foreach (var version in _supportedVersions)
            {
                report.Add($"  - {version.Key} (程序集版本: {version.Value})");
            }

            // 检查配置文件设置
            var configVersions = ConfigurationManager.AppSettings["SupportedTeklaVersions"];
            if (!string.IsNullOrEmpty(configVersions))
            {
                report.Add("");
                report.Add($"配置文件中的支持版本: {configVersions}");
            }

            return string.Join(Environment.NewLine, report);
        }

        /// <summary>
        /// 检查程序集绑定重定向是否正确配置
        /// </summary>
        /// <returns>配置检查结果</returns>
        public static bool ValidateBindingRedirects()
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                // 这里可以添加更详细的配置验证逻辑
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError($"验证程序集绑定重定向失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取推荐的配置设置
        /// </summary>
        /// <returns>推荐配置</returns>
        public static string GetRecommendedConfiguration()
        {
            var installedVersions = GetInstalledTeklaVersions();
            if (!installedVersions.Any())
            {
                return "未检测到已安装的Tekla Structures版本";
            }

            var latestVersion = installedVersions.Last();
            var recommendedConfig = $@"
推荐配置 (基于检测到的最新版本 {latestVersion}):

1. 在App.config中设置程序集绑定重定向
2. 使用 SpecificVersion=False 的程序集引用
3. 确保GAC中有对应版本的程序集

当前配置状态:
- 程序集绑定重定向: {(ValidateBindingRedirects() ? "已配置" : "需要配置")}
- 支持的版本: {string.Join(", ", _supportedVersions.Keys)}
";

            return recommendedConfig;
        }
    }
}
